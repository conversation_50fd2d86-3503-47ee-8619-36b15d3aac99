# 图片查看器功能说明

## 功能概述

新增的图片查看器组件 (`ImageViewer.vue`) 为 uniapp 项目提供了强大的图片浏览功能，支持左右滑动查看多张图片，提供了更好的用户体验。

## 主要特性

### 1. 滑动浏览
- 支持左右滑动切换图片
- 使用 `swiper` 组件实现流畅的滑动效果
- 支持手势操作和按钮操作

### 2. 图片计数
- 显示当前图片位置和总图片数量
- 格式：`当前图片 / 总图片数`
- 实时更新当前位置

### 3. 缩略图导航
- 底部显示所有图片的缩略图
- 点击缩略图可快速跳转到对应图片
- 当前图片的缩略图会高亮显示
- 支持横向滚动查看更多缩略图

### 4. 操作按钮
- 左右切换按钮：快速切换到上一张/下一张图片
- 关闭按钮：退出图片查看器
- 按钮在边界时会自动禁用（如第一张图片时左按钮禁用）

### 5. 全屏体验
- 全屏显示图片
- 黑色半透明背景
- 图片自适应屏幕大小

## 使用方法

### 1. 组件导入
```javascript
import ImageViewer from '@/components/ImageViewer.vue'

export default {
  components: {
    ImageViewer
  }
}
```

### 2. 模板使用
```vue
<template>
  <view>
    <!-- 图片列表 -->
    <view v-for="(image, index) in images" :key="index" @click="openImageViewer(images, index)">
      <image :src="image" mode="aspectFill" />
    </view>
    
    <!-- 图片查看器 -->
    <ImageViewer 
      :visible="imageViewerVisible"
      :images="currentImages"
      :initialIndex="currentImageIndex"
      :showThumbnails="true"
      @close="closeImageViewer"
      @change="onImageChange"
    />
  </view>
</template>
```

### 3. 数据和方法
```javascript
export default {
  data() {
    return {
      imageViewerVisible: false,
      currentImages: [],
      currentImageIndex: 0
    }
  },
  methods: {
    openImageViewer(images, index = 0) {
      this.currentImages = images
      this.currentImageIndex = index
      this.imageViewerVisible = true
    },
    
    closeImageViewer() {
      this.imageViewerVisible = false
      this.currentImages = []
      this.currentImageIndex = 0
    },
    
    onImageChange(index) {
      this.currentImageIndex = index
    }
  }
}
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | Boolean | false | 是否显示图片查看器 |
| images | Array | [] | 图片URL数组 |
| initialIndex | Number | 0 | 初始显示的图片索引 |
| showThumbnails | Boolean | true | 是否显示缩略图导航 |

## 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| close | - | 关闭图片查看器时触发 |
| change | index | 切换图片时触发，参数为当前图片索引 |

## 已集成页面

### 1. 首页 (`pages/index/index.vue`)
- 案例列表中的图片点击查看
- 支持查看案例的所有图片
- 从点击的图片开始显示

### 2. 案例详情页 (`pages/case/detail.vue`)
- 案例详情中的图片点击查看
- 显示案例的完整图片集合
- 支持全屏浏览

### 3. 测试页面 (`pages/test/image-viewer.vue`)
- 专门的测试页面
- 演示各种使用场景
- 可以测试不同的配置选项

## 技术实现

### 1. 图片URL处理
- 修复了原有的图片URL处理逻辑
- 支持完整URL和相对路径
- 预览时使用高质量图片

### 2. 滑动实现
- 使用 `swiper` 组件实现
- 禁用自动播放和循环
- 支持手势和按钮操作

### 3. 缩略图滚动
- 使用 `scroll-view` 实现横向滚动
- 自动计算滚动位置
- 当前图片居中显示

## 样式特点

- 全屏覆盖设计
- 黑色半透明背景 (rgba(0, 0, 0, 0.9))
- 圆角按钮设计
- 响应式布局
- 支持不同屏幕尺寸

## 注意事项

1. 确保传入的图片URL数组不为空
2. 图片加载失败时会显示错误提示
3. 组件使用固定定位，z-index 为 9999
4. 建议在图片较多时启用缩略图导航
5. 组件会自动处理边界情况（第一张/最后一张图片）

## 测试建议

1. 访问测试页面：`pages/test/image-viewer`
2. 测试不同数量的图片（1张、多张）
3. 测试滑动、点击、按钮操作
4. 测试缩略图导航功能
5. 测试在不同设备上的显示效果
