{
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "成功案例",
        "navigationBarBackgroundColor": "#007AFF",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/case/detail",
      "style": {
        "navigationBarTitleText": "案例详情",
        "navigationBarBackgroundColor": "#007AFF",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/user/profile",
      "style": {
        "navigationBarTitleText": "用户主页",
        "navigationBarBackgroundColor": "#007AFF",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/test/image-viewer",
      "style": {
        "navigationBarTitleText": "图片查看器测试",
        "navigationBarBackgroundColor": "#007AFF",
        "navigationBarTextStyle": "white"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "white",
    "navigationBarTitleText": "成功案例",
    "navigationBarBackgroundColor": "#007AFF",
    "backgroundColor": "#F5F5F5"
  },
  "uniIdRouter": {}
}
