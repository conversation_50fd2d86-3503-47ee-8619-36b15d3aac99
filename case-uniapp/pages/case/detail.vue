<template>
  <view class="container">
    <view class="case-detail" v-if="caseInfo">
      <!-- 用户信息 -->
      <view class="user-section" @click="goToUserProfile">
        <image class="user-avatar" :src="processAvatarUrl(caseInfo.publisher.avatar)" mode="aspectFill"></image>
        <view class="user-info">
          <text class="user-name">{{ caseInfo.publisher.nickName }}</text>
          <text class="publish-time">{{ formatTime(caseInfo.createTime) }}</text>
        </view>
      </view>

      <!-- 案例标签 -->
      <view class="case-tags" v-if="caseInfo.caseTags">
        <text class="tag" v-for="tag in processTags(caseInfo.caseTags)" :key="tag">#{{ tag }}</text>
      </view>

      <!-- 案例标题 -->
      <view class="case-title">{{ caseInfo.caseTitle }}</view>

      <!-- 案例内容 -->
      <view class="case-content">
        <rich-text :nodes="caseInfo.caseContent"></rich-text>
      </view>

      <!-- 案例图片 -->
      <view class="case-images" v-if="caseInfo.caseImages">
        <view class="image-grid">
          <view class="image-item" v-for="(image, index) in processImages(caseInfo.caseImages)" :key="index"
            @click="openImageViewer(processImages(caseInfo.caseImages), index)">
            <image class="case-image" :src="image" mode="aspectFill"></image>
          </view>
        </view>
      </view>

      <!-- 点击统计 -->
      <view class="stats-section">
        <view class="stats-item">
          <text class="stats-label">点击次数</text>
          <text class="stats-value">{{ caseInfo.clickCount }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-section">
        <button class="action-btn share-btn" @click="shareCase">分享案例</button>
        <button class="action-btn poster-btn" @click="generatePoster">生成海报</button>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading" v-else>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 海报弹窗 -->
    <view class="poster-modal" v-if="showPosterModal" @click="closePosterModal">
      <view class="poster-content" @click.stop>
        <view class="poster-header">
          <text class="poster-title">案例海报</text>
          <view class="poster-close" @click="closePosterModal">×</view>
        </view>

        <view class="poster-canvas-container">
          <canvas class="poster-canvas" canvas-id="posterCanvas"
            :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"></canvas>
        </view>

        <view class="poster-controls">
          <button class="control-btn" @click="togglePosterContent">
            {{ showFullContent ? '显示部分内容' : '显示全部内容' }}
          </button>
          <button class="control-btn primary" @click="savePoster">保存海报</button>
        </view>
      </view>
    </view>

    <!-- 图片查看器 -->
    <ImageViewer :visible="imageViewerVisible" :images="currentImages" :initialIndex="currentImageIndex"
      @close="closeImageViewer" @change="onImageChange" />
  </view>
</template>

<script>
import { caseInfoApi } from '@/utils/api.js'
import { formatTime, processImages, processAvatarUrl, processTags, previewImages, stripHtml, generateQRData } from '@/utils/utils.js'
import ImageViewer from '@/components/ImageViewer.vue'

export default {
  components: {
    ImageViewer
  },
  data() {
    return {
      caseId: null,
      caseInfo: null,
      showPosterModal: false,
      showFullContent: false,
      // 图片查看器相关
      imageViewerVisible: false,
      currentImages: [],
      currentImageIndex: 0,
      canvasWidth: 300,
      canvasHeight: 400
    }
  },

  onLoad(options) {
    this.caseId = options.caseId
    if (this.caseId) {
      this.loadCaseDetail()
    }
  },

  methods: {
    // 加载案例详情
    async loadCaseDetail() {
      try {
        const res = await caseInfoApi.getCaseDetail(this.caseId)
        this.caseInfo = res.data

        // 设置页面标题
        uni.setNavigationBarTitle({
          title: this.caseInfo.caseTitle
        })
      } catch (error) {
        console.error('加载案例详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    // 跳转到用户主页
    goToUserProfile() {
      uni.navigateTo({
        url: `/pages/user/profile?userId=${this.caseInfo.publisherId}`
      })
    },

    // 分享案例
    shareCase() {
      const shareData = {
        title: this.caseInfo.caseTitle,
        path: `/pages/case/detail?caseId=${this.caseId}`,
        imageUrl: this.processImages(this.caseInfo.caseImages)[0] || ''
      }

      // #ifdef MP-WEIXIN
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      })
      // #endif

      uni.showToast({
        title: '请使用右上角分享',
        icon: 'none'
      })
    },

    // 生成海报
    generatePoster() {
      this.showPosterModal = true
      this.$nextTick(() => {
        this.drawPoster()
      })
    },

    // 绘制海报
    drawPoster() {
      const ctx = uni.createCanvasContext('posterCanvas', this)
      const { canvasWidth, canvasHeight } = this

      // 清空画布
      ctx.clearRect(0, 0, canvasWidth, canvasHeight)

      // 设置背景色
      ctx.setFillStyle('#ffffff')
      ctx.fillRect(0, 0, canvasWidth, canvasHeight)

      let y = 20

      // 绘制标题
      ctx.setFillStyle('#333333')
      ctx.setFontSize(16)
      ctx.setTextAlign('left')

      const title = this.caseInfo.caseTitle
      const titleLines = this.wrapText(ctx, title, canvasWidth - 40, 16)
      titleLines.forEach(line => {
        ctx.fillText(line, 20, y)
        y += 20
      })

      y += 10

      // 绘制内容
      const content = stripHtml(this.caseInfo.caseContent)
      const displayContent = this.showFullContent ? content : content.substring(0, 200) + '...'

      ctx.setFillStyle('#666666')
      ctx.setFontSize(12)

      const contentLines = this.wrapText(ctx, displayContent, canvasWidth - 40, 12)
      const maxLines = this.showFullContent ? contentLines.length : Math.min(contentLines.length, 10)

      for (let i = 0; i < maxLines; i++) {
        ctx.fillText(contentLines[i], 20, y)
        y += 16
      }

      // 如果内容被截断，显示提示线
      if (!this.showFullContent && content.length > 200) {
        y += 10
        ctx.setStrokeStyle('#cccccc')
        ctx.setLineWidth(1)
        ctx.beginPath()
        ctx.moveTo(20, y)
        ctx.lineTo(canvasWidth - 20, y)
        ctx.stroke()

        ctx.setFillStyle('#999999')
        ctx.setFontSize(10)
        ctx.setTextAlign('center')
        ctx.fillText('部分内容已隐藏', canvasWidth / 2, y + 15)
        y += 25
      }

      // 绘制二维码区域（这里用文字代替）
      y = canvasHeight - 60
      ctx.setFillStyle('#333333')
      ctx.setFontSize(10)
      ctx.setTextAlign('center')
      ctx.fillText('扫码查看原文', canvasWidth / 2, y)
      ctx.fillText(generateQRData(this.caseId), canvasWidth / 2, y + 15)

      ctx.draw()
    },

    // 文字换行处理
    wrapText(ctx, text, maxWidth, fontSize) {
      const words = text.split('')
      const lines = []
      let currentLine = ''

      for (let i = 0; i < words.length; i++) {
        const testLine = currentLine + words[i]
        const metrics = ctx.measureText(testLine)
        const testWidth = metrics.width

        if (testWidth > maxWidth && i > 0) {
          lines.push(currentLine)
          currentLine = words[i]
        } else {
          currentLine = testLine
        }
      }
      lines.push(currentLine)
      return lines
    },

    // 切换海报内容显示
    togglePosterContent() {
      this.showFullContent = !this.showFullContent
      this.drawPoster()
    },

    // 保存海报
    savePoster() {
      uni.canvasToTempFilePath({
        canvasId: 'posterCanvas',
        success: (res) => {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              uni.showToast({
                title: '保存成功',
                icon: 'success'
              })
              this.closePosterModal()
            },
            fail: () => {
              uni.showToast({
                title: '保存失败',
                icon: 'none'
              })
            }
          })
        },
        fail: () => {
          uni.showToast({
            title: '生成失败',
            icon: 'none'
          })
        }
      }, this)
    },

    // 关闭海报弹窗
    closePosterModal() {
      this.showPosterModal = false
      this.showFullContent = false
    },

    // 打开图片查看器
    openImageViewer(images, index = 0) {
      this.currentImages = images
      this.currentImageIndex = index
      this.imageViewerVisible = true
    },

    // 关闭图片查看器
    closeImageViewer() {
      this.imageViewerVisible = false
      this.currentImages = []
      this.currentImageIndex = 0
    },

    // 图片切换事件
    onImageChange(index) {
      this.currentImageIndex = index
    },

    // 工具方法
    formatTime,
    processImages,
    processAvatarUrl,
    processTags,
    previewImages
  }
}
</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.case-detail {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

/* 用户信息 */
.user-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.publish-time {
  font-size: 26rpx;
  color: #999;
}

/* 案例标签 */
.case-tags {
  margin-bottom: 20rpx;
}

.tag {
  display: inline-block;
  background-color: #f0f8ff;
  color: #007AFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  margin-right: 12rpx;
  margin-bottom: 10rpx;
}

/* 案例标题 */
.case-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 30rpx;
}

/* 案例内容 */
.case-content {
  font-size: 30rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 30rpx;
}

/* 案例图片 */
.case-images {
  margin-bottom: 30rpx;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 15rpx;
}

.image-item {
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.case-image {
  width: 100%;
  height: 100%;
}

/* 统计信息 */
.stats-section {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-label {
  font-size: 28rpx;
  color: #666;
}

.stats-value {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

/* 操作按钮 */
.action-section {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-btn {
  background-color: #f0f8ff;
  color: #007AFF;
}

.poster-btn {
  background-color: #007AFF;
  color: #fff;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 海报弹窗 */
.poster-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.poster-content {
  background-color: #fff;
  border-radius: 16rpx;
  width: 80%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.poster-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.poster-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.poster-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.poster-canvas-container {
  padding: 30rpx;
  display: flex;
  justify-content: center;
}

.poster-canvas {
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
}

.poster-controls {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.control-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  font-size: 26rpx;
  border: 1rpx solid #e0e0e0;
  background-color: #fff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn.primary {
  background-color: #007AFF;
  color: #fff;
  border-color: #007AFF;
}
</style>
