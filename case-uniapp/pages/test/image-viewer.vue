<template>
  <view class="container">
    <view class="header">
      <text class="title">图片查看器测试</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">测试图片组</text>
      
      <!-- 测试图片网格 -->
      <view class="image-grid">
        <view 
          class="image-item" 
          v-for="(image, index) in testImages" 
          :key="index"
          @click="openImageViewer(testImages, index)"
        >
          <image class="test-image" :src="image" mode="aspectFill" />
          <view class="image-index">{{ index + 1 }}</view>
        </view>
      </view>
      
      <!-- 功能按钮 -->
      <view class="button-group">
        <button class="test-btn" @click="openImageViewer(testImages, 0)">
          从第1张开始查看
        </button>
        <button class="test-btn" @click="openImageViewer(testImages, 2)">
          从第3张开始查看
        </button>
        <button class="test-btn" @click="openImageViewer([testImages[0]], 0)">
          查看单张图片
        </button>
      </view>
    </view>
    
    <!-- 图片查看器 -->
    <ImageViewer 
      :visible="imageViewerVisible"
      :images="currentImages"
      :initialIndex="currentImageIndex"
      :showThumbnails="showThumbnails"
      @close="closeImageViewer"
      @change="onImageChange"
    />
    
    <!-- 设置面板 -->
    <view class="settings-panel">
      <text class="panel-title">设置</text>
      <view class="setting-item">
        <text class="setting-label">显示缩略图导航</text>
        <switch :checked="showThumbnails" @change="onThumbnailToggle" />
      </view>
      <view class="info-item">
        <text class="info-label">当前图片索引：</text>
        <text class="info-value">{{ currentImageIndex + 1 }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import ImageViewer from '@/components/ImageViewer.vue'

export default {
  components: {
    ImageViewer
  },
  data() {
    return {
      // 测试图片数组
      testImages: [
        'https://picsum.photos/800/600?random=1',
        'https://picsum.photos/800/600?random=2', 
        'https://picsum.photos/800/600?random=3',
        'https://picsum.photos/800/600?random=4',
        'https://picsum.photos/800/600?random=5',
        'https://picsum.photos/800/600?random=6'
      ],
      // 图片查看器相关
      imageViewerVisible: false,
      currentImages: [],
      currentImageIndex: 0,
      showThumbnails: true
    }
  },
  methods: {
    // 打开图片查看器
    openImageViewer(images, index = 0) {
      this.currentImages = images
      this.currentImageIndex = index
      this.imageViewerVisible = true
      console.log('打开图片查看器:', { images: images.length, index })
    },

    // 关闭图片查看器
    closeImageViewer() {
      this.imageViewerVisible = false
      this.currentImages = []
      this.currentImageIndex = 0
      console.log('关闭图片查看器')
    },

    // 图片切换事件
    onImageChange(index) {
      this.currentImageIndex = index
      console.log('切换到图片:', index + 1)
    },

    // 缩略图开关切换
    onThumbnailToggle(e) {
      this.showThumbnails = e.detail.value
    }
  }
}
</script>

<style scoped>
.container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.image-item {
  position: relative;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid #e0e0e0;
}

.test-image {
  width: 100%;
  height: 100%;
}

.image-index {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.test-btn {
  height: 80rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-panel {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-label {
  font-size: 28rpx;
  color: #333;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #007aff;
  font-weight: bold;
}
</style>
