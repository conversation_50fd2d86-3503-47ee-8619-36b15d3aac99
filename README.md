# 案例管理系统

基于若依框架和 UniApp 开发的案例管理系统，包含 Web 管理后台和移动端应用。

## 项目结构

```
case-management/
├── case-api/           # 后端API服务（基于若依框架）
│   ├── ruoyi-admin/    # 管理后台模块
│   ├── ruoyi-system/   # 系统核心模块
│   ├── ruoyi-ui/       # 前端Vue项目
│   └── sql/            # 数据库脚本
├── case-uniapp/        # UniApp移动端项目
└── README.md           # 项目说明文档
```

## 功能特性

### Web 管理后台

- 案例用户管理：用户信息的增删改查，支持头像上传
- 案例信息管理：案例的增删改查，支持富文本编辑和多图片上传
- 权限管理：基于若依框架的完整权限体系
- 数据导入导出：支持 Excel 格式的批量操作

### UniApp 移动端

- 首页展示：优秀客户展示和成功案例列表
- 案例详情：完整的案例信息展示和图片预览
- 用户主页：用户信息和发布的案例列表
- 海报生成：将案例保存为海报，支持二维码分享
- **图片滑动查看**：支持左右滑动查看所有案例图片，包含缩略图导航和图片计数

## 技术栈

### 后端

- Spring Boot 2.x
- Spring Security
- MyBatis
- MySQL 5.7+
- Redis（可选）

### 前端管理后台

- Vue 2.x
- Element UI
- Axios

### 移动端

- UniApp
- Vue 2.x
- 原生小程序 API

## 快速开始

### 1. 环境要求

- JDK 1.8+
- Node.js 14+
- MySQL 5.7+
- Maven 3.6+

### 2. 数据库初始化

```sql
-- 创建数据库
CREATE DATABASE ry-vue DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 导入数据库脚本
mysql -u root -p ry-vue < case-api/sql/ry_20250522.sql
```

### 3. 后端启动

```bash
cd case-api
# 修改数据库配置 ruoyi-admin/src/main/resources/application-druid.yml
# 启动后端服务
mvn clean install
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

### 4. 前端管理后台启动

```bash
cd case-api/ruoyi-ui
npm install
npm run dev
```

### 5. UniApp 项目启动

```bash
cd case-uniapp
# 使用HBuilderX打开项目
# 或使用uni-app CLI
npm install -g @vue/cli @vue/cli-init
vue init dcloudio/uni-preset-vue case-uniapp
```

## 配置说明

### 数据库配置

修改 `case-api/ruoyi-admin/src/main/resources/application-druid.yml`：

```yaml
spring:
  datasource:
    druid:
      master:
        url: ***********************************************************************************************************************************************
        username: root
        password: your_password
```

### UniApp API 配置

修改 `case-uniapp/utils/api.js`：

```javascript
const BASE_URL = "http://your-domain:8080"; // 修改为实际的后端地址
```

## 部署说明

### 后端部署

1. 打包项目：`mvn clean package`
2. 上传 jar 包到服务器
3. 启动服务：`java -jar ruoyi-admin.jar`

### 前端部署

1. 构建项目：`npm run build:prod`
2. 将 dist 目录部署到 Nginx
3. 配置 Nginx 反向代理

### UniApp 部署

1. 使用 HBuilderX 发行到各个平台
2. 微信小程序：发布到微信开发者工具
3. App：生成安装包

## API 文档

详细的 API 接口文档请参考：[test-api.md](case-api/test-api.md)

## 默认账号

- 管理员账号：admin / admin123
- 普通用户：ry / 123456

## 项目截图

### Web 管理后台

- 案例用户管理页面
- 案例信息管理页面
- 富文本编辑器
- 图片上传组件

### UniApp 移动端

- 首页优秀客户展示
- 案例列表页面
- 案例详情页面
- 用户个人主页
- 海报生成功能

## 开发指南

### 添加新功能

1. 后端：按照若依框架的分层结构开发
2. 前端：使用 Element UI 组件库
3. 移动端：遵循 UniApp 开发规范

### 代码规范

- 后端：遵循阿里巴巴 Java 开发手册
- 前端：使用 ESLint 进行代码检查
- 数据库：遵循数据库设计规范

## 常见问题

### 1. 数据库连接失败

- 检查数据库服务是否启动
- 确认用户名密码是否正确
- 验证数据库 URL 格式

### 2. 前端页面空白

- 检查后端服务是否启动
- 确认 API 接口地址是否正确
- 查看浏览器控制台错误信息

### 3. UniApp 编译失败

- 检查 Node.js 版本是否符合要求
- 清除缓存重新安装依赖
- 确认 HBuilderX 版本是否最新

## 更新日志

### v1.1.0 (2025-08-26)

- ✅ 新增自定义图片查看器组件 (`ImageViewer.vue`)
- ✅ 支持图片左右滑动查看功能
- ✅ 添加图片计数显示 (当前图片/总图片数)
- ✅ 集成缩略图导航栏，支持快速跳转
- ✅ 添加左右切换按钮
- ✅ 优化图片 URL 处理逻辑
- ✅ 首页和详情页均已集成新的图片查看功能
- ✅ 创建图片查看器测试页面

### v1.0.0 (2024-01-01)

- 初始版本发布
- 完成基础功能开发
- 支持 Web 管理后台和 UniApp 移动端

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 发起 Pull Request

## 许可证

本项目采用 MIT 许可证，详情请参阅 [LICENSE](LICENSE) 文件。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱：<EMAIL>
- 微信：your-wechat-id
- QQ 群：123456789
